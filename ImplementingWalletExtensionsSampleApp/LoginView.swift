/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
The view for the main (containing) app login.
*/

import SwiftUI
import PassKit
import os

// MARK: - Shared Models

/**
 This structure represents the data that can be stored to support wallet extension functionality.
 This matches the structure used in the extension.
 */
struct ProvisioningCredential: Equatable, Codable, Hashable {
    var primaryAccountIdentifier: String
    var label: String
    var assetName: String
    var isAvailableForProvisioning: Bool
    var cardholderName: String
    var localizedDescription: String
    var primaryAccountSuffix: String
    var expiration: String
}

// MARK: - App Group Configuration

// Set the app group ID to match the extension
let appGroupID: String = "group.com.corpay.drivenai"

// Create an object that can connect to the user's defaults database within the app group container
let appGroupSharedDefaults: UserDefaults = UserDefaults(suiteName: appGroupID) ?? UserDefaults.standard

let log = Logger()

// MARK: - UI Components

struct AddPaymentPassViewController: UIViewControllerRepresentable {
    let card: ProvisioningCredential
    let onDismiss: () -> Void

    typealias UIViewControllerType = PKAddPaymentPassViewController

    func makeUIViewController(context: Context) -> PKAddPaymentPassViewController {
        // Create the configuration for adding a payment pass
        guard let configuration = PKAddPaymentPassRequestConfiguration(encryptionScheme: .ECC_V2) else {
            log.error("Failed to create PKAddPaymentPassRequestConfiguration")
            // Return a placeholder controller if configuration fails
            return PKAddPaymentPassViewController()
        }

        // Configure the request
        configuration.cardholderName = card.cardholderName
        configuration.primaryAccountSuffix = card.primaryAccountSuffix
        configuration.localizedDescription = card.localizedDescription
        configuration.primaryAccountIdentifier = card.primaryAccountIdentifier

        // Create the view controller
        guard let addPassViewController = PKAddPaymentPassViewController(requestConfiguration: configuration, delegate: context.coordinator) else {
            log.error("Failed to create PKAddPaymentPassViewController")
            return PKAddPaymentPassViewController()
        }

        return addPassViewController
    }

    func updateUIViewController(_ uiViewController: PKAddPaymentPassViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PKAddPaymentPassViewControllerDelegate {
        let parent: AddPaymentPassViewController

        init(_ parent: AddPaymentPassViewController) {
            self.parent = parent
        }

        func addPaymentPassViewController(_ controller: PKAddPaymentPassViewController, generateRequestWithCertificateChain certificates: [Data], nonce: Data, nonceSignature: Data, completionHandler handler: @escaping (PKAddPaymentPassRequest) -> Void) {

            // This is where you would normally make a network request to your server
            // to generate the encrypted pass data. For this demo, we'll create a mock request.
            let request = PKAddPaymentPassRequest()
            request.activationData = Data()
            request.encryptedPassData = Data()
            request.ephemeralPublicKey = Data()

        
            handler(request)
        }

        func addPaymentPassViewController(_ controller: PKAddPaymentPassViewController, didFinishAdding pass: PKPaymentPass?, error: Error?) {
            if let error = error {
                log.error("Failed to add payment pass: \(error.localizedDescription)")
            } else if let pass = pass {
                log.info("Successfully added payment pass: \(pass.localizedDescription)")
            }

            parent.onDismiss()
        }
    }
}

struct CardSelectionView: View {
    @Binding var availableCards: [ProvisioningCredential]
    let onAddCard: (ProvisioningCredential) -> Void

    var body: some View {
        NavigationView {
            VStack {
                if availableCards.isEmpty {
                    VStack(spacing: 20) {
                        Image(systemName: "creditcard")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        Text("No Cards Available")
                            .font(.title2)
                            .foregroundColor(.gray)
                        Text("All your cards have already been added to Apple Wallet or no cards are available for provisioning.")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                    }
                    .padding()
                } else {
                    List(availableCards, id: \.primaryAccountIdentifier) { card in
                        CardRowView(card: card) {
                            onAddCard(card)
                        }
                    }
                }
            }
            .navigationTitle("Add to Wallet")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

struct CardRowView: View {
    let card: ProvisioningCredential
    let onTap: () -> Void

    var body: some View {
        HStack {
            // Card icon
            Image(systemName: "creditcard.fill")
                .font(.system(size: 30))
                .foregroundColor(.blue)
                .frame(width: 50, height: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(card.label)
                    .font(.headline)
                Text("•••• \(card.primaryAccountSuffix)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Text("Expires \(card.expiration)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button(action: onTap) {
                HStack {
                    Image(systemName: "plus")
                    Text("Add")
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(20)
            }
        }
        .padding(.vertical, 8)
    }
}

struct WalletExtensionViewController: UIViewControllerRepresentable {
    @Binding var availableCards: [ProvisioningCredential]
    let onAddCard: (ProvisioningCredential) -> Void

    typealias UIViewControllerType = UIViewController

    func makeUIViewController(context: Context) -> UIViewController {
        let hostingController = UIHostingController(rootView: CardSelectionView(availableCards: $availableCards, onAddCard: onAddCard))
        return hostingController
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}

struct LoginView: View {
    @State var username: String = ""
    @State var password: String = ""
    @State private var showWalletExtension = false
    @State private var availableCards: [ProvisioningCredential] = []
    @State private var showAddPassViewController = false
    @State private var selectedCard: ProvisioningCredential?
    @State private var currentSheet: SheetType?

    enum SheetType: Identifiable {
        case walletExtension
        case addPaymentPass(ProvisioningCredential)

        var id: String {
            switch self {
            case .walletExtension:
                return "walletExtension"
            case .addPaymentPass(let card):
                return "addPaymentPass_\(card.primaryAccountIdentifier)"
            }
        }
    }
    
    /**
     Handle a tap on the Log In button.
     */
    func handleLogin() {
        // Create username/password logic.
        print("Log In button tapped")
    }
    
    /**
     Handle a tap on the Face ID button.
     */
    func handleBiometricLogin() {
        // Create biometric login logic.
        print("Face ID button tapped")
    }
    
    /**
     Load available cards from shared app group data and setup sample data if needed.
     */
    func loadAvailableCards() {
        // First, ensure we have some sample data
        setupSampleCardData()

        // Load available cards from shared app group data
        if let cachedCredentialsData = appGroupSharedDefaults.data(forKey: "PaymentPassCredentials") {
            if let decoded = try? JSONDecoder().decode([String: ProvisioningCredential].self, from: cachedCredentialsData) {
                // Filter out cards that are already in Apple Pay
                let passLibrary = PKPassLibrary()
                let existingPasses = passLibrary.passes(of: .secureElement)
                var existingIdentifiers: Set<String> = []

                for pass in existingPasses {
                    if !pass.isRemotePass, let identifier = pass.secureElementPass?.primaryAccountIdentifier {
                        existingIdentifiers.insert(identifier)
                    }
                }

                // Only show cards that aren't already added
                availableCards = decoded.values.filter { credential in
                    !existingIdentifiers.contains(credential.primaryAccountIdentifier) &&
                    credential.isAvailableForProvisioning
                }

                log.info("Loaded \(availableCards.count) available cards for provisioning")
            }
        } else {
            log.warning("No cached credentials found in app group")
        }
    }

    /**
     Setup sample card data for demonstration purposes.
     */
    func setupSampleCardData() {
        // Check if we already have data
        if appGroupSharedDefaults.data(forKey: "PaymentPassCredentials") != nil {
            return
        }

        // Create sample cards
        let sampleCards: [String: ProvisioningCredential] = [
            "123": ProvisioningCredential(
                primaryAccountIdentifier: "123",
                label: "Demo Visa Card",
                assetName: "visa_card",
                isAvailableForProvisioning: true,
                cardholderName: "Demo User",
                localizedDescription: "Demo Visa ending in 1234",
                primaryAccountSuffix: "1234",
                expiration: "12/28"
            ),
            "456": ProvisioningCredential(
                primaryAccountIdentifier: "456",
                label: "Demo Mastercard",
                assetName: "mastercard_card",
                isAvailableForProvisioning: true,
                cardholderName: "Demo User",
                localizedDescription: "Demo Mastercard ending in 5678",
                primaryAccountSuffix: "5678",
                expiration: "06/29"
            )
        ]

        // Save to shared app group
        if let encoded = try? JSONEncoder().encode(sampleCards) {
            appGroupSharedDefaults.set(encoded, forKey: "PaymentPassCredentials")
            appGroupSharedDefaults.set(true, forKey: "ShouldRequireAuthenticationForAppleWallet")
            log.info("Sample card data created and saved to app group")
        }
    }

    /**
     Start the process to add a specific card to Apple Wallet.
     */
    func addCardToWallet(_ card: ProvisioningCredential) {
        // Dismiss the current sheet first, then show the add payment pass sheet
        currentSheet = nil
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            currentSheet = .addPaymentPass(card)
        }
        log.info("Starting add to wallet flow for card: \(card.label)")
    }

    func startWalletExtensionFlow() {
        // Load available cards and show the wallet extension interface
        loadAvailableCards()
        currentSheet = .walletExtension
        print("Starting wallet extension flow...")
    }
   
    var body: some View {
        VStack {
            let smallConfig = UIImage.SymbolConfiguration(pointSize: 50, weight: .bold, scale: .small)
            if let banknoteLogo = UIImage(systemName: "banknote.fill", withConfiguration: smallConfig) {
                Image(uiImage: banknoteLogo.withRenderingMode(.alwaysTemplate))
                    .foregroundColor(.white)
                    .padding([.bottom], 10)
            }
            Text("Implementing Wallet Extensions Sample App")
                .font(.title)
                .bold()
                .padding([.bottom], 20)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            List {
                Section(header: Text("Login")) {
                    HStack {
                        Label("", systemImage: "person")
                        Spacer()
                        TextField("Username", text: $username)
                            .onAppear {
                                if username.isEmpty {
                                    username = "DemoUser"
                                }
                            }
                            .bold()
                    }
                    .accessibilityElement(children: .ignore)
                    .accessibilityLabel("Username Text Field")
                    .contentShape(Rectangle())
                    HStack {
                        Label("", systemImage: "lock")
                        Spacer()
                        SecureField("Password", text: $password)
                            .onAppear {
                                if password.isEmpty {
                                    password = "fakepassword"
                                }
                            }
                            .bold()
                    }
                    .accessibilityElement(children: .ignore)
                    .accessibilityLabel("Password Text Field")
                    .contentShape(Rectangle())
                }
                .padding(12)
                HStack(spacing: 18) {
                    Spacer()
                    Button(
                        action: handleBiometricLogin,
                        label: {
                            HStack {
                                Image(systemName: "faceid")
                                Text("Face ID")
                                    .bold()
                                    .font(.system(size: 16.0))
                            }
                            .padding(6)
                        }
                    )
                    .buttonStyle(.bordered)
                    .background(Color.blue)
                    .cornerRadius(26)
                    .foregroundColor(.white)
                    Button(
                        action: handleLogin,
                        label: {
                            Text("Log In")
                                .bold()
                                .font(.system(size: 16.0))
                                .padding(6)
                                .frame(width: 70)
                        }
                    )
                    .buttonStyle(.bordered)
                    .background(Color.orange)
                    .cornerRadius(26)
                    .foregroundColor(.white)
                }
                .listRowBackground(Color.clear)
                Button(action: startWalletExtensionFlow) {
                    HStack {
                        Image(systemName: "wallet.pass")
                        Text("Add Card to Wallet")
                            .bold()
                    }
                    .padding(6)
                }
                .buttonStyle(.borderedProminent)
                .background(Color.green)
                .cornerRadius(26)
                .foregroundColor(.white)
                .padding(.top, 10)
            }
            
        }
        .background(Color.blue)
        .onAppear {
            loadAvailableCards()
        }
        .sheet(item: $currentSheet) { sheetType in
            switch sheetType {
            case .walletExtension:
                WalletExtensionViewController(availableCards: $availableCards) { card in
                    addCardToWallet(card)
                }
            case .addPaymentPass(let card):
                AddPaymentPassViewController(card: card) {
                    currentSheet = nil
                    // Refresh available cards after adding a card
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        loadAvailableCards()
                    }
                }
            }
        }
    }
}

#Preview {
    LoginView()
}
