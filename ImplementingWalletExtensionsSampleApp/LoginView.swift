/*
See the LICENSE.txt file for this sample’s licensing information.

Abstract:
The view for the main (containing) app login.
*/

import SwiftUI
import PassKit

struct WalletExtensionViewController: UIViewControllerRepresentable {
    typealias UIViewControllerType = UIViewController

    func makeUIViewController(context: Context) -> UIViewController {
        let viewController = UIViewController()
        viewController.view.backgroundColor = UIColor.systemBackground

        // Add a label to indicate this is a placeholder
        let label = UILabel()
        label.text = "Wallet Extension Integration"
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        viewController.view.addSubview(label)

        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: viewController.view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: viewController.view.centerYAnchor)
        ])

        return viewController
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {}
}

struct LoginView: View {
    @State var username: String = ""
    @State var password: String = ""
    @State private var showWalletExtension = false
    
    /**
     Handle a tap on the Log In button.
     */
    func handleLogin() {
        // Create username/password logic.
        print("Log In button tapped")
    }
    
    /**
     Handle a tap on the Face ID button.
     */
    func handleBiometricLogin() {
        // Create biometric login logic.
        print("Face ID button tapped")
    }
    
    func startWalletExtensionFlow() {
        if PKIssuerProvisioningExtension.isAvailable() {
            // Start the wallet extension flow using the proper PassKit APIs
            // This would typically involve calling PKIssuerProvisioningExtension methods
            // to initiate the provisioning process
            showWalletExtension = true
            print("Starting wallet extension flow...")
        } else {
            print("Wallet Extension is not available.")
        }
    }
   
    var body: some View {
        VStack {
            let smallConfig = UIImage.SymbolConfiguration(pointSize: 50, weight: .bold, scale: .small)
            if let banknoteLogo = UIImage(systemName: "banknote.fill", withConfiguration: smallConfig) {
                Image(uiImage: banknoteLogo.withRenderingMode(.alwaysTemplate))
                    .foregroundColor(.white)
                    .padding([.bottom], 10)
            }
            Text("Implementing Wallet Extensions Sample App")
                .font(.title)
                .bold()
                .padding([.bottom], 20)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            List {
                Section(header: Text("Login")) {
                    HStack {
                        Label("", systemImage: "person")
                        Spacer()
                        TextField("Username", text: $username)
                            .onAppear {
                                if username.isEmpty {
                                    username = "DemoUser"
                                }
                            }
                            .bold()
                    }
                    .accessibilityElement(children: .ignore)
                    .accessibilityLabel("Username Text Field")
                    .contentShape(Rectangle())
                    HStack {
                        Label("", systemImage: "lock")
                        Spacer()
                        SecureField("Password", text: $password)
                            .onAppear {
                                if password.isEmpty {
                                    password = "fakepassword"
                                }
                            }
                            .bold()
                    }
                    .accessibilityElement(children: .ignore)
                    .accessibilityLabel("Password Text Field")
                    .contentShape(Rectangle())
                }
                .padding(12)
                HStack(spacing: 18) {
                    Spacer()
                    Button(
                        action: handleBiometricLogin,
                        label: {
                            HStack {
                                Image(systemName: "faceid")
                                Text("Face ID")
                                    .bold()
                                    .font(.system(size: 16.0))
                            }
                            .padding(6)
                        }
                    )
                    .buttonStyle(.bordered)
                    .background(Color.blue)
                    .cornerRadius(26)
                    .foregroundColor(.white)
                    Button(
                        action: handleLogin,
                        label: {
                            Text("Log In")
                                .bold()
                                .font(.system(size: 16.0))
                                .padding(6)
                                .frame(width: 70)
                        }
                    )
                    .buttonStyle(.bordered)
                    .background(Color.orange)
                    .cornerRadius(26)
                    .foregroundColor(.white)
                }
                .listRowBackground(Color.clear)
                Button(action: startWalletExtensionFlow) {
                    HStack {
                        Image(systemName: "wallet.pass")
                        Text("Add Card to Wallet")
                            .bold()
                    }
                    .padding(6)
                }
                .buttonStyle(.borderedProminent)
                .background(Color.green)
                .cornerRadius(26)
                .foregroundColor(.white)
                .padding(.top, 10)
            }
            VStack {
                Text("This start page is a demo login view.")
                    .foregroundColor(.white)
                    .padding()
                    .multilineTextAlignment(.center)
                    .fontWeight(.thin)
                HStack {
                    Link("Terms of Use",
                         destination: URL(string: "https://example.com")!)
                    Text("|")
                    Link("Privacy Policy",
                         destination: URL(string: "https://example.com")!)
                }
                .font(.system(size: 12))
                .foregroundColor(.white)
                .fontWeight(.light)
            }
        }
        .background(Color.blue)
        .sheet(isPresented: $showWalletExtension) {
            WalletExtensionViewController()
        }
    }
}

#Preview {
    LoginView()
}
