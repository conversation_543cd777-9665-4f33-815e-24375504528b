<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1520"
   wasCreatedForAppExtension = "YES"
   version = "2.0">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F28450712B99123000C6FCA7"
               BuildableName = "WNonUIExt.appex"
               BlueprintName = "WNonUIExt"
               ReferencedContainer = "container:ImplementingWalletExtensionsSampleApp.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F2C2FAD22B9235E600155069"
               BuildableName = "ImplementingWalletExtensionsSampleApp.app"
               BlueprintName = "ImplementingWalletExtensionsSampleApp"
               ReferencedContainer = "container:ImplementingWalletExtensionsSampleApp.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES"
            useTestSelectionWhitelist = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F2FAE7D22BC7235F009BB6AA"
               BuildableName = "ImplementingWalletExtensionsSampleAppTests.xctest"
               BlueprintName = "ImplementingWalletExtensionsSampleAppTests"
               ReferencedContainer = "container:ImplementingWalletExtensionsSampleApp.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      launchStyle = "0"
      askForAppToLaunch = "YES"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F2C2FAD22B9235E600155069"
            BuildableName = "ImplementingWalletExtensionsSampleApp.app"
            BlueprintName = "ImplementingWalletExtensionsSampleApp"
            ReferencedContainer = "container:ImplementingWalletExtensionsSampleApp.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES"
      launchAutomaticallySubstyle = "2"
      askForAppToLaunch = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F2C2FAD22B9235E600155069"
            BuildableName = "ImplementingWalletExtensionsSampleApp.app"
            BlueprintName = "ImplementingWalletExtensionsSampleApp"
            ReferencedContainer = "container:ImplementingWalletExtensionsSampleApp.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
